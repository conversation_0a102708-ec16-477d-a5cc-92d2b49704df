# remember on implement

- using typescript
- update contentTypes.d.ts file
- use throw error correct the error by using errors in the strapi/utils

```
import { errors } from '@strapi/utils';
const { ApplicationError, NotFoundError } = errors;
```

- use snake_case
- validate the data input from the client

- let read document and implement by use the document instead of entityService https://docs.strapi.io/cms/api/document-service
