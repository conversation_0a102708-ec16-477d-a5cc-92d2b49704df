import { S3Client, PutObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3';
import fs from 'fs';
import path from 'path';

interface S3UploadConfig {
  accessKeyId: string;
  secretAccessKey: string;
  region: string;
  bucket: string;
  rootPath?: string;
}

interface S3UploadResult {
  fileKey: string;
  fileName: string;
  publicUrl: string;
  bucket: string;
  region: string;
}

class S3UploadService {
  private s3Client: S3Client;
  private config: S3UploadConfig;

  constructor() {
    this.config = {
      accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
      secretAccessKey: process.env.AWS_ACCESS_SECRET || '',
      region: process.env.AWS_REGION || 'us-east-1',
      bucket: process.env.AWS_BUCKET || '',
      rootPath: process.env.ROOT_PATH_UPLOAD || 'strapi',
    };

    // Validate required configuration
    if (!this.config.accessKeyId || !this.config.secretAccessKey || !this.config.bucket) {
      throw new Error('Missing required AWS S3 credentials. Please check environment variables.');
    }

    // Initialize S3 client
    this.s3Client = new S3Client({
      region: this.config.region,
      credentials: {
        accessKeyId: this.config.accessKeyId,
        secretAccessKey: this.config.secretAccessKey,
      },
    });

    console.log('S3 Upload service initialized successfully');
  }

  /**
   * Upload a file to S3
   * @param filePath - Local path to the file to upload
   * @param fileName - Name for the file in S3
   * @param folder - Optional folder path within the bucket
   * @returns Promise with upload result information
   */
  async uploadFile(
    filePath: string,
    fileName: string,
    folder?: string
  ): Promise<S3UploadResult> {
    try {
      // Check if file exists
      if (!fs.existsSync(filePath)) {
        throw new Error(`File not found: ${filePath}`);
      }

      // Get file stats and content
      const stats = fs.statSync(filePath);
      const fileContent = fs.readFileSync(filePath);
      
      console.log(`Uploading file: ${fileName} (${stats.size} bytes)`);

      // Determine content type based on file extension
      const ext = path.extname(fileName).toLowerCase();
      let contentType = 'application/octet-stream';
      
      switch (ext) {
        case '.jpg':
        case '.jpeg':
          contentType = 'image/jpeg';
          break;
        case '.png':
          contentType = 'image/png';
          break;
        case '.gif':
          contentType = 'image/gif';
          break;
        case '.webp':
          contentType = 'image/webp';
          break;
        case '.bmp':
          contentType = 'image/bmp';
          break;
        case '.svg':
          contentType = 'image/svg+xml';
          break;
        default:
          contentType = 'application/octet-stream';
      }

      // Construct the S3 key (file path in bucket)
      let fileKey = fileName;
      if (this.config.rootPath) {
        fileKey = `${this.config.rootPath}/${fileKey}`;
      }
      if (folder) {
        fileKey = `${this.config.rootPath}/${folder}/${fileName}`;
      }

      // Upload to S3
      const uploadCommand = new PutObjectCommand({
        Bucket: this.config.bucket,
        Key: fileKey,
        Body: fileContent,
        ContentType: contentType,
        ACL: 'public-read', // Make file publicly accessible
        CacheControl: 'max-age=31536000', // Cache for 1 year
      });

      await this.s3Client.send(uploadCommand);

      // Generate public URL
      const publicUrl = `https://${this.config.bucket}.s3.${this.config.region}.amazonaws.com/${fileKey}`;

      console.log(`File uploaded successfully to S3: ${publicUrl}`);

      return {
        fileKey,
        fileName,
        publicUrl,
        bucket: this.config.bucket,
        region: this.config.region,
      };
    } catch (error) {
      console.error('Error uploading file to S3:', error);
      throw error;
    }
  }

  /**
   * Upload an image specifically with additional validation
   * @param imagePath - Local path to the image file
   * @param fileName - Name for the file in S3
   * @param folder - Optional folder path within the bucket
   * @returns Promise with upload result information
   */
  async uploadImage(
    imagePath: string,
    fileName: string,
    folder?: string
  ): Promise<S3UploadResult> {
    try {
      // Validate that it's an image file
      const ext = path.extname(fileName).toLowerCase();
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.svg'];
      
      if (!imageExtensions.includes(ext)) {
        throw new Error(`Invalid image file extension: ${ext}`);
      }

      // Use the general upload method
      return await this.uploadFile(imagePath, fileName, folder);
    } catch (error) {
      console.error('Error uploading image to S3:', error);
      throw error;
    }
  }

  /**
   * Delete a file from S3
   * @param fileKey - S3 key of the file to delete
   */
  async deleteFile(fileKey: string): Promise<void> {
    try {
      const deleteCommand = new DeleteObjectCommand({
        Bucket: this.config.bucket,
        Key: fileKey,
      });

      await this.s3Client.send(deleteCommand);
      console.log(`File deleted successfully from S3: ${fileKey}`);
    } catch (error) {
      console.error('Error deleting file from S3:', error);
      throw error;
    }
  }

  /**
   * Generate a unique filename for TikTok covers
   * @param videoId - TikTok video ID
   * @param originalExtension - Original file extension
   * @returns Unique filename
   */
  generateTikTokCoverFileName(videoId: string, originalExtension: string): string {
    const timestamp = Date.now();
    const cleanExt = originalExtension.startsWith('.') ? originalExtension : `.${originalExtension}`;
    return `tiktok_cover_${videoId}${cleanExt}`;
  }

  /**
   * Get the S3 configuration
   * @returns Current S3 configuration
   */
  getConfig(): S3UploadConfig {
    return { ...this.config };
  }
}

// Export singleton instance
export const s3UploadService = new S3UploadService();
export default s3UploadService;
