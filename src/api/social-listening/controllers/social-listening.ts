import { factories } from '@strapi/strapi';
import { YoutubeTranscriptClient } from '../../../utils/request';

export default factories.createCoreController(
  'api::social-listening.social-listening',
  ({ strapi }) => ({
    // Custom action
    async searchKeyword(ctx) {
      try {
        const { affiliateId } = ctx.params;
        let { platforms } = ctx.query;
        if (typeof platforms === 'string' && platforms.includes(',')) {
          platforms = platforms.split(',');
        }
        const result = await strapi
          .service('api::social-listening.social-listening')
          .searchKeyword(affiliateId, [platforms].flat());
        ctx.body = result;
      } catch (err) {
        console.error('LOG-err', err);
        ctx.body = err;
      }
    },

    async getTranscript(ctx) {
      try {
        console.log('ctx.params', ctx.params);
        const { user } = ctx.state;
        const { videoId } = ctx.params;
        if (!videoId) {
          return ctx.badRequest('VideoId is required');
        }
        const result = await strapi
          .service('api::social-listening.social-listening')
          .getTranscript(videoId, user.id);
        ctx.body = result;
      } catch (err) {
        console.error('LOG-err', err);
        ctx.body = err;
      }
    },

    async getTiktokDownloadLink(ctx) {
      try {
        const { videoId } = ctx.params;

        if (!videoId) {
          return ctx.badRequest('VideoId is required');
        }

        const result = await strapi
          .service('api::social-listening.social-listening')
          .getTiktokDownloadLink(videoId);

        ctx.body = result;
      } catch (err) {
        console.error('Error getting TikTok download link:', err);
        ctx.body = {
          success: false,
          error: err.message,
        };
      }
    },

    async getTiktokVideoDetailWithCover(ctx) {
      try {
        const { videoId } = ctx.params;

        if (!videoId) {
          ctx.status = 400;
          ctx.body = {
            success: false,
            error: 'Video ID is required',
          };
          return;
        }

        console.log(`Controller: Getting TikTok video detail with cover for video ID: ${videoId}`);

        const result = await strapi
          .service('api::social-listening.social-listening')
          .getTiktokVideoDetailWithCover(videoId);

        if (result.success) {
          ctx.status = 200;
        } else {
          ctx.status = 500;
        }

        ctx.body = result;
      } catch (err) {
        console.error('Error in getTiktokVideoDetailWithCover controller:', err);
        ctx.status = 500;
        ctx.body = {
          success: false,
          error: err.message || 'Internal server error',
        };
      }
    },

    async find(ctx) {
      const filters: any = ctx.query.filters;
      const platforms = filters?.platform?.$in || [];
      try {
        const affiliate = filters?.affiliate || {};
        console.log('LOG-platforms', platforms);
        console.log('LOG-affiliateDocId', affiliate.documentId);
        console.time('LOG-processAffiliateSocialData');

        await strapi
          .service('api::social-listening.social-listening')
          .processAffiliateSocialData(affiliate?.documentId, platforms);
        console.timeEnd('LOG-processAffiliateSocialData');

        const aff = await strapi
          .documents('api::affiliate.affiliate')
          .findOne({ documentId: affiliate?.documentId });
        if (aff && aff.monthly_traffic && typeof aff.monthly_traffic === 'string') {
          filters.views = {
            $lte: parseInt(aff.monthly_traffic) * 2,
          };
        }
        // Add is_displayed true filter to the query
        ctx.query.filters = {
          ...filters,
          is_displayed: true,
        };

        // Call the default core action
        const { data, meta } = await super.find(ctx);
        return { data, meta };
      } catch (err) {
        console.error('LOG-err', err);
        return err;
      }
    },

    async getCustomTranscript(ctx) {
      try {
        const { videoId } = ctx.params;

        if (!videoId) {
          return ctx.badRequest('VideoId is required');
        }

        console.log(`Getting custom transcript for YouTube video: ${videoId}`);

        // Directly use the YoutubeTranscriptClient to get the transcript
        const result = await YoutubeTranscriptClient.getTranscript(videoId, true);

        // Return the transcript data
        ctx.body = {
          success: true,
          videoId,
          transcript: result.transcript,
          source: result.source,
        };
      } catch (err) {
        console.error('Error getting custom YouTube transcript:', err);
        ctx.body = {
          success: false,
          error: err.message || 'An error occurred while fetching the transcript',
          videoId: ctx.params.videoId,
        };
      }
    },

    async crawlTiktok(ctx) {
      try {
        const { affiliateId } = ctx.request.body;

        if (!affiliateId) {
          return ctx.badRequest('Affiliate ID is required');
        }

        console.log(`Starting TikTok crawl for affiliate: ${affiliateId}`);

        // Get affiliate to retrieve keywords
        const affiliate = await strapi.documents('api::affiliate.affiliate').findOne({
          documentId: affiliateId,
        });

        if (!affiliate) {
          return ctx.badRequest(`Affiliate with ID ${affiliateId} not found`);
        }

        if (!affiliate.brand_keywords_tiktok) {
          return ctx.badRequest(`No TikTok keywords found for affiliate ${affiliate.name}`);
        }

        // Split pipe-separated keywords
        const keywords = affiliate.brand_keywords_tiktok.split('|').filter(Boolean);

        if (keywords.length === 0) {
          return ctx.badRequest(`No valid TikTok keywords found for affiliate ${affiliate.name}`);
        }

        console.log(
          `Found ${keywords.length} TikTok keywords for affiliate ${affiliate.name}: ${keywords.join(', ')}`
        );

        // Process each keyword
        const results = [];
        for (const keyword of keywords) {
          console.log(`Processing TikTok keyword: "${keyword}" for affiliate: ${affiliate.name}`);
          const keywordResult = await strapi
            .service('api::social-listening.social-listening')
            .crawlTiktokData(keyword, affiliateId);

          results.push(keywordResult);
        }

        ctx.body = {
          success: true,
          message: `TikTok crawl completed successfully for ${keywords.length} keywords`,
          affiliate: affiliate.name,
          keywords: keywords,
          results: results,
        };
      } catch (err) {
        console.error('Error in crawlTiktok controller:', err);
        ctx.body = {
          success: false,
          error: err.message || 'An error occurred while crawling TikTok data',
        };
      }
    },

    async crawlYoutube(ctx) {
      try {
        const { affiliateId } = ctx.request.body;

        if (!affiliateId) {
          return ctx.badRequest('Affiliate ID is required');
        }

        console.log(`Starting YouTube crawl for affiliate: ${affiliateId}`);

        // Get affiliate to retrieve keywords
        const affiliate = await strapi.documents('api::affiliate.affiliate').findOne({
          documentId: affiliateId,
        });

        if (!affiliate) {
          return ctx.badRequest(`Affiliate with ID ${affiliateId} not found`);
        }

        if (!affiliate.brand_keywords_youtube) {
          return ctx.badRequest(`No YouTube keywords found for affiliate ${affiliate.name}`);
        }

        // Split pipe-separated keywords
        const keywords = affiliate.brand_keywords_youtube.split('|').filter(Boolean);

        if (keywords.length === 0) {
          return ctx.badRequest(`No valid YouTube keywords found for affiliate ${affiliate.name}`);
        }

        console.log(
          `Found ${keywords.length} YouTube keywords for affiliate ${affiliate.name}: ${keywords.join(', ')}`
        );

        // Process each keyword
        const results = [];
        for (const keyword of keywords) {
          console.log(`Processing YouTube keyword: "${keyword}" for affiliate: ${affiliate.name}`);
          const keywordResult = await strapi
            .service('api::social-listening.social-listening')
            .crawlYoutubeData(keyword, affiliateId);

          results.push(keywordResult);
        }

        ctx.body = {
          success: true,
          message: `YouTube crawl completed successfully for ${keywords.length} keywords`,
          affiliate: affiliate.name,
          keywords: keywords,
          results: results,
        };
      } catch (err) {
        console.error('Error in crawlYoutube controller:', err);
        ctx.body = {
          success: false,
          error: err.message || 'An error occurred while crawling YouTube data',
        };
      }
    },
  })
);
