{"kind": "collectionType", "collectionName": "chat_sessions", "info": {"singularName": "chat-session", "pluralName": "chat-sessions", "displayName": "Chat Session", "description": "Stores chat session data"}, "options": {"draftAndPublish": false}, "attributes": {"session_id": {"type": "string", "required": true, "unique": true}, "start_time": {"type": "datetime", "required": true}, "end_time": {"type": "datetime"}, "session_status": {"type": "enumeration", "enum": ["active", "ended"], "default": "active", "required": true}, "messages": {"type": "relation", "relation": "oneToMany", "target": "api::chat-message.chat-message", "mappedBy": "session"}, "users_permissions_user": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}}}