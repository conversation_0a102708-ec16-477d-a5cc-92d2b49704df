/**
 * referrer controller
 */

import { factories } from '@strapi/strapi';

export default factories.createCoreController('api::referrer.referrer', ({ strapi }) => ({
  async register(ctx) {
    try {
      // Call the custom service method
      const result = await strapi.service('api::referrer.referrer').register({
        user: ctx.state.user,
      });

      return result;
    } catch (err) {
      ctx.body = err;
      return ctx.badRequest('Registration failed', { error: err.message });
    }
  },

  async dashboard(ctx) {
    try {
      // Get total count of referrers in the system
      const totalReferrers = await strapi.entityService.count('api::referrer.referrer');

      // Get total count of all referrals in the system with referral_status 'conversion'
      const totalReferrals = await strapi.entityService.count('api::referral.referral', {
        filters: { referral_status: 'conversion' },
      });

      // Get all referrer links to calculate total clicks and leads
      const allReferrerLinks = await strapi.entityService.findMany(
        'api::referrer-link.referrer-link',
        {
          fields: ['visitors', 'leads', 'conversions'],
        }
      );

      // Calculate total clicks (visitors) and total leads from all referrer links
      const totalClicks = allReferrerLinks.reduce((sum, link) => sum + (link.visitors || 0), 0);
      const totalLeads = allReferrerLinks.reduce((sum, link) => sum + (link.leads || 0), 0);

      // Get total revenue from all referral commissions in the system
      const allReferralCommissions = await strapi.entityService.findMany(
        'api::referral-commission.referral-commission',
        {
          fields: ['gross_sale_amount'],
        }
      );

      const totalRevenue = allReferralCommissions.reduce((sum, commission) => {
        const amount = commission.gross_sale_amount;
        if (typeof amount === 'number') {
          return sum + amount;
        } else if (typeof amount === 'string') {
          return sum + (parseFloat(amount) || 0);
        }
        return sum;
      }, 0);

      const recentActivity = await strapi.entityService.findMany(
        'api::referral-activity.referral-activity',
        {
          sort: { createdAt: 'desc' },
          limit: 10,
          populate: {
            referral: {
              populate: {
                user: true,
              },
            },
          },
        }
      );

      const previousMonthRevenue = await strapi.entityService.findMany(
        'api::referral-commission.referral-commission',
        {
          filters: {
            createdAt: {
              $lt: new Date(new Date().setMonth(new Date().getMonth() - 1)),
            },
          },
          fields: ['gross_sale_amount'],
        }
      );

      const previousMonthRevenueAmount = previousMonthRevenue.reduce((sum, commission) => {
        const amount = commission.gross_sale_amount;
        if (typeof amount === 'number') {
          return sum + amount;
        } else if (typeof amount === 'string') {
          return sum + (parseFloat(amount) || 0);
        }
        return sum;
      }, 0);

      const currentMonthRevenue = totalRevenue - previousMonthRevenueAmount;
      const growthRate =
        previousMonthRevenueAmount > 0
          ? ((currentMonthRevenue - previousMonthRevenueAmount) / previousMonthRevenueAmount) * 100
          : currentMonthRevenue > 0
            ? 100
            : 0;

      const topReferrers = await strapi.entityService.findMany('api::referrer.referrer', {
        sort: { total_earnings: 'desc' },
        limit: 10,
        populate: {
          user: {
            fields: ['username', 'email'],
          },
        },
      });

      const topReferrals = await strapi.entityService.findMany('api::referral.referral', {
        sort: { createdAt: 'desc' },
        limit: 10,
        populate: {
          user: {
            fields: ['username', 'email'],
          },
        },
      });

      return {
        success: true,
        data: {
          totalReferrers,
          totalReferrals,
          totalClicks,
          totalLeads,
          totalRevenue: parseFloat(totalRevenue.toFixed(2)),
          recentActivity,
          topReferrers,
          topReferrals,
          previousMonthRevenueAmount: parseFloat(previousMonthRevenueAmount.toFixed(2)),
          currentMonthRevenue: parseFloat(currentMonthRevenue.toFixed(2)),
          growthRate: parseFloat(growthRate.toFixed(2)),
        },
      };
    } catch (err) {
      ctx.body = err;
      return ctx.badRequest('Dashboard data retrieval failed', { error: err.message });
    }
  },
}));
