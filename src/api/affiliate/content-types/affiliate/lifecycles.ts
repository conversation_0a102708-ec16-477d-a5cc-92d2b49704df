import { errors } from '@strapi/utils';
const { ApplicationError } = errors;

/**
 * Generate a slug from a string by lowercasing and replacing spaces with hyphens
 */
function generateSlug(name: string): string {
  if (!name) return '';
  return name
    .toLowerCase()
    .replace(/\s+/g, '-')
    .replace(/[^a-z0-9-]/g, '');
}

/**
 * Validation functions for different aspects of affiliate data
 */

// Handle slug generation
async function validateSlug(data) {
  if (data.name) {
    data.slug = generateSlug(data.name);
  }
  return data;
}

// Calculate recurring priority for sorting
async function calculateRecurringPriority(data) {
  if (!data.recurring) {
    data.recurring_priority = null;
    return data;
  }

  const recurringPriorityMap = {
    'Life time': 14,
    'One time': 13,
    'In 36 months': 12,
    'In 24 months': 11,
    'In 18 months': 10,
    'In 15 months': 9,
    'In 12 months': 8,
    'In 9 months': 7,
    'In 6 months': 6,
    'In 5 months': 5,
    'In 4 months': 4,
    'In 3 months': 3,
    'In 2 months': 2,
    'In 1 month': 1,
  };

  data.recurring_priority = recurringPriorityMap[data.recurring] || null;
  return data;
}

// Calculate average price from pricing range
async function calculateAveragePrice(data) {
  try {
    if (data.pricing_range && data.pricing_range.id) {
      try {
        const pricingRangeComponent = await strapi.db.query('shared.pricing-range').findOne({
          where: { id: data.pricing_range.id },
        });

        console.log('Pricing Range Component:', pricingRangeComponent);

        if (
          pricingRangeComponent &&
          pricingRangeComponent.from !== undefined &&
          pricingRangeComponent.to !== undefined
        ) {
          try {
            const minPrice = parseFloat(pricingRangeComponent.from);
            const maxPrice = parseFloat(pricingRangeComponent.to);

            // Convert currency code to symbol
            let currencySymbol = '$'; // Default to $ (USD)
            if (pricingRangeComponent.currency) {
              switch (pricingRangeComponent.currency.toUpperCase()) {
                case 'EUR':
                  currencySymbol = '€';
                  break;
                case 'USD':
                default:
                  currencySymbol = '$';
                  break;
              }
            }

            if (!isNaN(minPrice) && !isNaN(maxPrice)) {
              // Validate that min price is not greater than max price
              if (minPrice > maxPrice) {
                console.error('Min price is greater than max price. Cannot calculate average.');
                return {
                  isValid: false,
                  error: 'Minimum price cannot be greater than maximum price',
                  data,
                };
              }

              // Calculate average price
              const avgPrice = (minPrice + maxPrice) / 2;
              data.avg_price = avgPrice;

              // Format the pricing field based on whether min and max prices are equal
              // If prices are equal, only show one price
              data.pricing =
                minPrice === maxPrice
                  ? `${currencySymbol}${minPrice}`
                  : `${currencySymbol}${minPrice} - ${currencySymbol}${maxPrice}`;
              console.log(
                `Calculated average price: ${avgPrice} from min: ${minPrice} and max: ${maxPrice}`
              );
              console.log(`Formatted pricing string: ${data.pricing}`);
            }
          } catch (parseError) {
            console.error('Error parsing price values:', parseError);
            return {
              isValid: false,
              error: 'Invalid price format. Please use valid numbers.',
              data,
            };
          }
        }
      } catch (componentError) {
        console.error('Error fetching pricing component:', componentError);
        return {
          isValid: false,
          error: 'Could not retrieve pricing information',
          data,
        };
      }
    }
    return { isValid: true, data };
  } catch (error) {
    console.error('Unexpected error in price calculation:', error);
    return {
      isValid: false,
      error: 'An unexpected error occurred while processing pricing information',
      data,
    };
  }
}

// Validate domain format (example validator)
async function validateDomain(data) {
  if (data.domain) {
    // Simple domain validation - remove http/https and www.
    data.domain = data.domain.replace(/^(https?:\/\/)?(www\.)?/, '').replace(/\/$/, ''); // Remove trailing slash
  }
  return data;
}

/**
 * Main handler that composes all validation functions
 */
async function beforeSave(event) {
  let { data } = event.params;

  // Apply slug validation
  data = await validateSlug(data);

  // Apply recurring priority calculation
  data = await calculateRecurringPriority(data);

  // Apply pricing validation
  const pricingResult = await calculateAveragePrice(data);
  if (!pricingResult.isValid) {
    // Use ApplicationError instead of regular Error for proper error handling in the admin UI
    throw new ApplicationError(pricingResult.error);
  }
  data = pricingResult.data;

  // Apply domain validation
  data = await validateDomain(data);

  // Update the event params with validated data
  event.params.data = data;
}

/**
 * Tracks search keywords and updates their usage count in the database
 */
async function trackSearchKeyword(textSearch: string) {
  if (!textSearch) return;

  const existingKeyword = await strapi.query('api::search-keyword.search-keyword').findOne({
    where: { keyword: textSearch },
  });

  if (existingKeyword) {
    await strapi.query('api::search-keyword.search-keyword').update({
      where: { id: existingKeyword.id },
      data: { count: existingKeyword.count + 1 },
    });
  } else {
    await strapi.query('api::search-keyword.search-keyword').create({
      data: { keyword: textSearch, count: 1 },
    });
  }
}

async function afterFindMany(event) {
  try {
    const { params } = event;
    const textSearch = params?.where?.name?.['$containsi'];

    if (textSearch) {
      await trackSearchKeyword(textSearch);
    }

    // Request counting is now handled by the middleware
  } catch (error) {
    console.log('LOG-error', error);
  }
}

async function beforeFindMany(event) {
  // Authentication and request limit validation now handled by middleware
  const { params } = event;
  const recurringMap = {
    life_time: ['Life time'],
    one_time: ['One time'],
    less_than_12: [
      'In 1 month',
      'In 2 months',
      'In 3 months',
      'In 4 months',
      'In 5 months',
      'In 6 months',
      'In 9 months',
    ],
    more_than_or_equal_12: [
      'In 12 months',
      'In 15 months',
      'In 18 months',
      'In 24 months',
      'In 36 months',
      'Life time',
    ],
  };

  // Handle recurring filter
  const { where } = params;
  if (where && where.recurring) {
    where.recurring = {
      $in: recurringMap[where.recurring],
    };
  }

  // Handle sorting for recurring field - check both sort and orderBy parameters
  if (params.orderBy && Array.isArray(params.orderBy)) {
    console.log('Original orderBy:', params.orderBy);

    // Process each orderBy item
    params.orderBy = params.orderBy.map((orderItem: Record<string, unknown>) => {
      if (orderItem && typeof orderItem === 'object') {
        // Check if this is sorting by recurring field
        if (orderItem.recurring) {
          console.log('Found recurring sort, replacing with recurring_priority');
          // Replace recurring with recurring_priority
          const newOrderItem = { ...orderItem };
          newOrderItem.recurring_priority = orderItem.recurring;
          delete newOrderItem.recurring;
          return newOrderItem;
        }
      }
      return orderItem;
    });

    console.log('Updated orderBy:', params.orderBy);
  }

  // Also handle legacy sort parameter if it exists
  if (params.sort) {
    console.log('Original sort parameter:', params.sort);

    const sortArray = Array.isArray(params.sort) ? params.sort : [params.sort];

    const updatedSort = sortArray.map((sortItem: string | object) => {
      if (typeof sortItem === 'string' && sortItem.startsWith('recurring:')) {
        const direction = sortItem.endsWith(':desc') ? 'desc' : 'asc';
        console.log(`Replacing recurring:${direction} with recurring_priority:${direction}`);
        return `recurring_priority:${direction}`;
      }
      return sortItem;
    });

    params.sort = Array.isArray(params.sort) ? updatedSort : updatedSort[0];
    console.log('Updated sort parameter:', params.sort);
  }
}

export default {
  beforeCreate: beforeSave,
  beforeUpdate: beforeSave,
  afterFindMany,
  beforeFindMany,
};

// Export for testing
export { calculateRecurringPriority };
