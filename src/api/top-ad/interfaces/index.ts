export interface IAd {
  ad_id: string;
  ad_title: string;
  brand_name: string;
  ctr: number;
  cost: number;
  industry_key: string;
  country_code: string[];
  landing_page: string;
  likes: number;
  comments: number;
  shares: number;
  objective_key: string;
  objectives: Array<{ label: string; value: number }>;
  source: string;
  video_info: {
    vid: string;
    duration: number;
    cover: string;
    video_url: {
      [key: string]: string;
    };
    width: number;
    height: number;
  };
  is_search: boolean;
  favorite: boolean;
  keyword?: string;
  platform: 'tiktok' | 'youtube';
  is_displayed?: boolean;
  last_fetched?: Date;
  views?: number;
  published_from?: Date;
}

export interface ITikTokAd extends IAd {
  platform: 'tiktok';
}

export interface ITikTokAdResponse {
  code: number;
  msg: string;
  request_id: string;
  data: {
    materials: Array<{
      ad_title: string;
      brand_name: string;
      cost: number;
      ctr: number;
      favorite: boolean;
      id: string;
      industry_key: string;
      is_search: boolean;
      like: number;
      objective_key: string;
      video_info: {
        vid: string;
        duration: number;
        cover: string;
        video_url: {
          [key: string]: string;
        };
        width: number;
        height: number;
      };
    }>;
    pagination: {
      has_more: boolean;
      page: number;
      size: number;
      total_count: number;
    };
  };
}

export interface ITikTokAdDetailResponse {
  code: number;
  msg: string;
  request_id: string;
  data: {
    ad_title: string;
    brand_name: string;
    comment: number;
    cost: number;
    country_code: string[];
    ctr: number;
    favorite: boolean;
    has_summary: boolean;
    highlight_text: string;
    id: string;
    industry_key: string;
    is_search: boolean;
    keyword_list: any;
    landing_page: string;
    like: number;
    objective_key: string;
    objectives: Array<{
      label: string;
      value: number;
    }>;
    pattern_label: any[];
    share: number;
    source: string;
    source_key: number;
    video_info: {
      vid: string;
      duration: number;
      cover: string;
      video_url: {
        [key: string]: string;
      };
      width: number;
      height: number;
    };
    voice_over: boolean;
  };
}

export interface IYoutubeAd extends IAd {
  platform: 'youtube';
  first_seen?: string;
  last_seen?: string;
  description?: string;
  ad_type?: number;
  cta_button?: string;
  networks?: number[];
  devices?: number[];
  publishers_count?: number;
  tracking_tools?: number[];
  aff_networks?: number[];
}

export interface IYoutubeAdResponse {
  total: number;
  records: Array<any>;
}
