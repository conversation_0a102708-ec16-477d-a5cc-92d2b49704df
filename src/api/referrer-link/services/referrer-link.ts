/**
 * referrer-link service
 */

import { factories } from '@strapi/strapi';
import { errors } from '@strapi/utils';
import { IUpdateReferrerLinkData } from '../interfaces/referrer-link.interface';
const { ApplicationError, NotFoundError } = errors;

export default factories.createCoreService('api::referrer-link.referrer-link', ({ strapi }) => ({
  async trackClickByUrl(url, user = null, shortLink = null) {
    try {
      if (!url && !shortLink) {
        throw new ApplicationError('Either URL or short link is required for tracking clicks');
      }

      // Priority 1: Try to find by short_link if provided
      let referrerLinks = [];
      let matchedBy = 'url';

      if (shortLink) {
        referrerLinks = await strapi.entityService.findMany('api::referrer-link.referrer-link', {
          filters: { short_link: shortLink },
          limit: 1,
          populate: ['referrer'],
        });

        if (referrerLinks?.length) {
          matchedBy = 'short_link';
          console.log('🎯 [Priority Tracking] Found referrer-link by short_link:', shortLink);
        }
      }

      // Priority 2: Fall back to URL matching if short_link not found and URL provided
      if (!referrerLinks?.length && url) {
        referrerLinks = await strapi.entityService.findMany('api::referrer-link.referrer-link', {
          filters: { url },
          limit: 1,
          populate: ['referrer'],
        });

        if (referrerLinks?.length) {
          console.log('🎯 [Priority Tracking] Found referrer-link by URL:', url);
        }
      }

      if (!referrerLinks?.length) {
        throw new NotFoundError(
          `No matching referrer link found for shortLink: ${shortLink} or URL: ${url}`
        );
      }

      const link: any = referrerLinks[0];

      const newVisitorCount = (link.visitors || 0) + 1;

      // Update the visitors count
      await strapi.entityService.update('api::referrer-link.referrer-link', link.id, {
        data: {
          visitors: newVisitorCount,
        },
      });

      // Record this tracking event
      await this.createTrackLink(link, 'visitors', user);

      // Update referrer totalClicks count
      if (link.referrer) {
        await this.updateReferrerTotalClicks(link.referrer.id);
      }

      // If user is provided and the link has a referrer, handle referral
      if (user && link.referrer) {
        // Check if the user already has a referral record
        const existingReferrals = await strapi.entityService.findMany('api::referral.referral', {
          filters: {
            user: user.id,
          },
          sort: { createdAt: 'desc' },
          limit: 1,
        });

        if (existingReferrals?.length > 0) {
          // Update existing referral with new referrer
          await strapi.entityService.update('api::referral.referral', existingReferrals[0].id, {
            data: {
              referrer: link.referrer.id,
              referrer_link: link.id,
            },
          });

          // Record this activity
          await this.saveReferralActivity({
            referral_status: 'click',
            description: 'User clicked referral link',
            referral: existingReferrals[0].id,
          });

          strapi.log.info(`Updated referral for user ${user.id} to referrer ${link.referrer.id}`);
        } else {
          // create referral
          await strapi.entityService.create('api::referral.referral', {
            data: {
              user: user.id,
              referrer: link.referrer.id,
              referrer_link: link.id,
              referral_status: 'cross',
            },
          });
        }
      }

      return {
        linkId: link.id,
        linkName: link.name,
        visitors: newVisitorCount,
        url: link.url,
        shortLink: link.short_link,
        matchedBy,
      };
    } catch (err) {
      strapi.log.error(`Error tracking click for URL ${url}:`, err);
      throw err;
    }
  },

  // Helper function to update referral status for a user
  async updateReferralForConversion(userId, amount = 0) {
    console.log('userId, amount', userId, amount);
    if (!userId) return;

    const referrals = await strapi.entityService.findMany('api::referral.referral', {
      filters: {
        user: userId,
      },
      sort: { createdAt: 'desc' },
      limit: 1,
    });

    if (referrals?.length > 0) {
      const referral = referrals[0];
      const newTotalAmount = (referral.total_paid || 0) + amount;

      console.log('newTotalAmount', newTotalAmount);

      await strapi.entityService.update('api::referral.referral', referral.id, {
        data: {
          referral_status: 'conversion',
          total_paid: newTotalAmount,
        },
      });

      return {
        referralId: referral.id,
        totalPaid: newTotalAmount,
      };
    }

    return null;
  },

  async trackConversionByUrl(url, user = null, amount = 0, tierName = '', shortLink = null) {
    try {
      if (!url && !shortLink) {
        throw new ApplicationError('Either URL or short link is required for tracking conversion');
      }

      strapi.log.info(
        'Step 1: Starting conversion tracking for URL:',
        url,
        'shortLink:',
        shortLink
      );

      // Priority 1: Try to find by short_link if provided
      let referrerLinks = [];
      let matchedBy = 'url';

      if (shortLink) {
        strapi.log.info('Step 2a: Searching for referrer link with short_link:', shortLink);
        referrerLinks = await strapi.entityService.findMany('api::referrer-link.referrer-link', {
          filters: { short_link: shortLink },
          limit: 1,
        });

        if (referrerLinks?.length) {
          matchedBy = 'short_link';
          strapi.log.info('🎯 [Priority Tracking] Found referrer-link by short_link:', shortLink);
        }
      }

      // Priority 2: Fall back to URL matching if short_link not found and URL provided
      if (!referrerLinks?.length && url) {
        strapi.log.info('Step 2b: Searching for referrer link with URL:', url);
        referrerLinks = await strapi.entityService.findMany('api::referrer-link.referrer-link', {
          filters: { url: url },
          limit: 1,
        });

        if (referrerLinks?.length) {
          strapi.log.info('🎯 [Priority Tracking] Found referrer-link by URL:', url);
        }
      }

      if (!referrerLinks?.length) {
        strapi.log.error(
          'Step 2 Failed: No matching referrer link found for shortLink:',
          shortLink,
          'or URL:',
          url
        );
        throw new NotFoundError(
          `No matching referrer link found for shortLink: ${shortLink} or URL: ${url}`
        );
      }

      strapi.log.info('Step 2 Success: Found referrer link:', referrerLinks[0].id);
      const link = referrerLinks[0];
      const newConversionCount = (link.conversions || 0) + 1;

      // Update the conversions count
      strapi.log.info(
        'Step 3: Updating conversions count from',
        link.conversions || 0,
        'to',
        newConversionCount
      );
      await strapi.entityService.update('api::referrer-link.referrer-link', link.id, {
        data: {
          conversions: newConversionCount,
        },
      });

      // Create a track link record for this conversion event
      await this.createTrackLink(link, 'conversions', user);

      // Update referrer totalConversions count
      const linkWithReferrer = (await strapi.entityService.findMany(
        'api::referrer-link.referrer-link',
        {
          filters: { id: link.id },
          populate: ['referrer'],
          limit: 1,
        }
      )) as Array<{ id: number; referrer?: { id: number } }>;
      if (linkWithReferrer?.length && linkWithReferrer[0].referrer) {
        await this.updateReferrerTotalConversions(linkWithReferrer[0].referrer.id);
      }

      // Update referral status if user exists
      let referralUpdate = null;
      if (user) {
        referralUpdate = await this.updateReferralForConversion(user.id, amount);

        // Get tier name from context or use a default
        const subscriptionDescription = `Subscribed to ${tierName} with amount ${amount}`;

        // Record this conversion in referral activity
        if (referralUpdate) {
          await this.saveReferralActivity({
            amount_paid: amount,
            referral_status: 'conversion',
            description: subscriptionDescription,
            referral: referralUpdate.referralId,
          });
        }
      }

      strapi.log.info(
        `Conversion tracked for link: ${link.name || 'unnamed'}. New conversions count: ${newConversionCount}, Amount: ${amount}`
      );

      return {
        linkId: link.id,
        linkName: link.name,
        conversions: newConversionCount,
        url: link.url,
        shortLink: link.short_link,
        amount,
        tierName,
        matchedBy,
      };
    } catch (error) {
      strapi.log.error(`Error tracking conversion for URL ${url}:`, error);
      throw error;
    }
  },

  async trackLeadByUrl(url, user = null, shortLink = null) {
    try {
      if (!url && !shortLink) {
        throw new ApplicationError('Either URL or short link is required for tracking lead');
      }

      strapi.log.info('Tracking lead for URL:', url, 'shortLink:', shortLink);

      // Priority 1: Try to find by short_link if provided
      let referrerLinks = [];
      let matchedBy = 'url';

      if (shortLink) {
        strapi.log.info('Searching for referrer link with short_link:', shortLink);
        referrerLinks = await strapi.entityService.findMany('api::referrer-link.referrer-link', {
          filters: { short_link: shortLink },
          limit: 1,
          populate: ['referrer'],
        });

        if (referrerLinks?.length) {
          matchedBy = 'short_link';
          strapi.log.info('🎯 [Priority Tracking] Found referrer-link by short_link:', shortLink);
        }
      }

      // Priority 2: Fall back to URL matching if short_link not found and URL provided
      if (!referrerLinks?.length && url) {
        strapi.log.info('Searching for referrer link with URL:', url);
        referrerLinks = await strapi.entityService.findMany('api::referrer-link.referrer-link', {
          filters: { url: url },
          limit: 1,
          populate: ['referrer'],
        });

        if (referrerLinks?.length) {
          strapi.log.info('🎯 [Priority Tracking] Found referrer-link by URL:', url);
        }
      }

      if (!referrerLinks || referrerLinks.length === 0) {
        throw new NotFoundError(
          `No matching referrer link found for shortLink: ${shortLink} or URL: ${url}`
        );
      }

      const link: any = referrerLinks[0];
      const newLeadCount = (link.leads || 0) + 1;

      // Update the leads count
      await strapi.entityService.update('api::referrer-link.referrer-link', link.id, {
        data: {
          leads: newLeadCount,
        },
      });

      // Create a track link record for this lead event
      await this.createTrackLink(link, 'leads', user);

      // Update referrer totalLeads count
      if (link.referrer) {
        await this.updateReferrerTotalLeads(link.referrer.id);
      }

      strapi.log.info(
        `Lead tracked for link: ${link.name || 'unnamed'}. New leads count: ${newLeadCount}`
      );

      // Create a referral record for this lead
      const referral = await strapi.entityService.create('api::referral.referral', {
        data: {
          user: user?.id,
          referrer: link?.referrer?.id,
          referrer_link: link.id,
          referral_status: 'lead',
        },
      });

      // Record this lead in referral activity
      if (referral) {
        await this.saveReferralActivity({
          referral_status: 'lead',
          description: 'Registered new user',
          referral: referral.id,
        });
      }

      return {
        linkId: link.id,
        linkName: link.name,
        leads: newLeadCount,
        url: link.url,
        shortLink: link.short_link,
        matchedBy,
      };
    } catch (error) {
      strapi.log.error(`Error tracking lead for URL ${url}:`, error);
      throw error;
    }
  },

  async createTrackLink(referrerLink, type, user = null) {
    try {
      if (!referrerLink || !type) {
        throw new ApplicationError('Referrer link and type are required');
      }

      strapi.log.info(`Creating track link for type: ${type}`, referrerLink);

      // Create the referrer link
      const createdLink = await strapi.entityService.create('api::track-link.track-link', {
        data: {
          referrer_link: referrerLink.id,
          referrer_url: referrerLink.url,
          type,
          user: user?.id,
        },
      });

      return createdLink;
    } catch (error) {
      strapi.log.error('Error creating track link:', error);
      throw error;
    }
  },

  async saveReferralActivity(referralActivity: {
    amount_paid?: number;
    referral_status?: string;
    description?: string;
    referral?: any;
  }) {
    try {
      if (!referralActivity) {
        throw new ApplicationError('Referral activity data is required');
      }

      strapi.log.info('Saving referral activity:', referralActivity);

      // Create the referral activity record
      const createdActivity = await strapi.entityService.create(
        'api::referral-activity.referral-activity',
        {
          data: referralActivity,
        }
      );

      return createdActivity;
    } catch (error) {
      strapi.log.error('Error saving referral activity:', error);
      throw error;
    }
  },

  async findByShortLink(shortLink) {
    try {
      if (!shortLink) {
        throw new ApplicationError('Short link is required');
      }

      // Find the referrer link by short_link
      const referrerLinks = await strapi.entityService.findMany(
        'api::referrer-link.referrer-link',
        {
          filters: { short_link: shortLink },
          populate: ['referrer', 'user'],
          limit: 1,
        }
      );

      if (!referrerLinks || referrerLinks.length === 0) {
        throw new NotFoundError(`No referrer link found with short link: ${shortLink}`);
      }

      return referrerLinks[0];
    } catch (error) {
      strapi.log.error(`Error finding referrer link by short link ${shortLink}:`, error);
      throw error;
    }
  },

  async trackClickByShortLink(shortLink, user = null) {
    try {
      if (!shortLink) {
        throw new ApplicationError('Short link is required for tracking clicks');
      }

      // Find the referrer link by short_link with referrer populated
      const referrerLinks = await strapi.entityService.findMany(
        'api::referrer-link.referrer-link',
        {
          filters: { short_link: shortLink },
          populate: ['referrer'],
          limit: 1,
        }
      );

      if (!referrerLinks || referrerLinks.length === 0) {
        throw new NotFoundError(`No referrer link found with short link: ${shortLink}`);
      }

      const link: any = referrerLinks[0];

      const newVisitorCount = (link.visitors || 0) + 1;

      // Update the visitors count
      await strapi.entityService.update('api::referrer-link.referrer-link', link.id, {
        data: {
          visitors: newVisitorCount,
        },
      });

      // Record this tracking event
      await this.createTrackLink(link, 'visitors', user);

      // Update referrer totalClicks count
      if (link.referrer) {
        await this.updateReferrerTotalClicks(link.referrer.id);
      }

      // If user is provided and the link has a referrer, handle referral
      if (user && link.referrer) {
        // Check if the user already has a referral record
        const existingReferrals = await strapi.entityService.findMany('api::referral.referral', {
          filters: {
            user: user.id,
          },
          sort: { createdAt: 'desc' },
          limit: 1,
        });

        if (existingReferrals?.length > 0) {
          // Update existing referral with new referrer
          await strapi.entityService.update('api::referral.referral', existingReferrals[0].id, {
            data: {
              referrer: link.referrer.id,
              referrer_link: link.id,
            },
          });

          // Record this activity
          await this.saveReferralActivity({
            referral_status: 'click',
            description: 'User clicked referral link via short link',
            referral: existingReferrals[0].id,
          });

          strapi.log.info(
            `Updated referral for user ${user.id} to referrer ${link.referrer.id} via short link`
          );
        } else {
          // create referral
          await strapi.entityService.create('api::referral.referral', {
            data: {
              user: user.id,
              referrer: link.referrer.id,
              referrer_link: link.id,
              referral_status: 'cross',
            },
          });
        }
      }

      return {
        linkId: link.id,
        linkName: link.name,
        visitors: newVisitorCount,
        url: link.url,
        shortLink: link.short_link,
      };
    } catch (err) {
      strapi.log.error(`Error tracking click for short link ${shortLink}:`, err);
      throw err;
    }
  },

  // Helper methods to update referrer totals
  async updateReferrerTotalClicks(referrerId: number) {
    try {
      // Get all referrer links for this referrer
      const referrerLinks = await strapi.entityService.findMany(
        'api::referrer-link.referrer-link',
        {
          filters: { referrer: { id: referrerId } },
          fields: ['visitors'],
        } as any
      );

      // Calculate total clicks (visitors) across all links
      const totalClicks = referrerLinks.reduce((sum, link) => sum + (link.visitors || 0), 0);

      // Update the referrer's totalClicks field
      await strapi.entityService.update('api::referrer.referrer', referrerId, {
        data: { totalClicks } as any,
      });

      strapi.log.info(`Updated referrer ${referrerId} totalClicks to ${totalClicks}`);
    } catch (error) {
      strapi.log.error(`Error updating referrer totalClicks for referrer ${referrerId}:`, error);
    }
  },

  async updateReferrerTotalLeads(referrerId: number) {
    try {
      // Get all referrer links for this referrer
      const referrerLinks = await strapi.entityService.findMany(
        'api::referrer-link.referrer-link',
        {
          filters: { referrer: { id: referrerId } },
          fields: ['leads'],
        } as any
      );

      // Calculate total leads across all links
      const totalLeads = referrerLinks.reduce((sum, link) => sum + (link.leads || 0), 0);

      // Update the referrer's totalLeads field
      await strapi.entityService.update('api::referrer.referrer', referrerId, {
        data: { totalLeads } as any,
      });

      strapi.log.info(`Updated referrer ${referrerId} totalLeads to ${totalLeads}`);
    } catch (error) {
      strapi.log.error(`Error updating referrer totalLeads for referrer ${referrerId}:`, error);
    }
  },

  async updateReferrerTotalConversions(referrerId: number) {
    try {
      // Get all referrer links for this referrer
      const referrerLinks = await strapi.entityService.findMany(
        'api::referrer-link.referrer-link',
        {
          filters: { referrer: { id: referrerId } },
          fields: ['conversions'],
        } as any
      );

      // Calculate total conversions across all links
      const totalConversions = referrerLinks.reduce(
        (sum, link) => sum + (link.conversions || 0),
        0
      );

      // Update the referrer's totalConversions field
      await strapi.entityService.update('api::referrer.referrer', referrerId, {
        data: { totalConversions } as any,
      });

      strapi.log.info(`Updated referrer ${referrerId} totalConversions to ${totalConversions}`);
    } catch (error) {
      strapi.log.error(
        `Error updating referrer totalConversions for referrer ${referrerId}:`,
        error
      );
    }
  },

  /**
   * Create an automatic referrer link when a page is published
   * @param page - The published page
   * @param referrer - The referrer associated with the page author
   */
  async createAutomaticReferrerLink(page: any, referrer: any) {
    try {
      console.log('🔗 createAutomaticReferrerLink called');
      console.log('   - Page ID:', page.id);
      console.log('   - Page Title:', page.title);
      console.log('   - Referrer ID:', referrer.id);
      console.log('   - Referral Code:', referrer.referral_code);

      // Get the base URL from environment or use default
      const baseUrl = process.env.FRONTEND_URL || 'https://affitor.com';
      console.log('   - Base URL:', baseUrl);

      // Construct the homepage URL with the via parameter
      const homepageUrl = new URL(baseUrl);
      homepageUrl.searchParams.set('via', referrer.referral_code);
      console.log('   - Homepage URL:', homepageUrl.toString());

      // Create a name for the referrer link
      const linkName = `${page.title} - Homepage Link`;
      console.log('   - Link Name:', linkName);

      // Check if a referrer link already exists for this page
      const existingLinks = await strapi.entityService.findMany(
        'api::referrer-link.referrer-link',
        {
          filters: {
            page: page.id,
            referrer: referrer.id,
          },
          limit: 1,
        }
      );

      // If a link already exists for this page and referrer, skip creation
      if (existingLinks && existingLinks.length > 0) {
        console.log('⚠️ Referrer link already exists for this page and referrer');
        console.log('   - Page ID:', page.id);
        console.log('   - Referrer ID:', referrer.id);
        console.log('   - Existing Link ID:', existingLinks[0].id);
        strapi.log.info('Referrer link already exists for this page and referrer');
        return existingLinks[0];
      }

      // Create the referrer link
      const referrerLinkData = {
        name: linkName,
        url: homepageUrl.toString(),
        visitors: 0,
        leads: 0,
        conversions: 0,
        direct_page_views: 0,
        referrer_link_views: 0,
        short_link_views: 0,
        referrer_sources: {},
        user: page.author.id,
        referrer: referrer.id,
        page: page.id,
      };

      const newReferrerLink = await strapi.entityService.create(
        'api::referrer-link.referrer-link',
        {
          data: referrerLinkData,
        }
      );

      strapi.log.info('Automatic referrer link created successfully', {
        linkId: newReferrerLink.id,
        pageId: page.id,
        pageTitle: page.title,
        referrerId: referrer.id,
        referralCode: referrer.referral_code,
        url: homepageUrl.toString(),
      });

      return newReferrerLink;
    } catch (error) {
      strapi.log.error('Error creating automatic referrer link:', error);
      throw error;
    }
  },
}));
