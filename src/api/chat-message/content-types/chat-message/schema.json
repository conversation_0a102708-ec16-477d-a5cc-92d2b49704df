{"kind": "collectionType", "collectionName": "chat_messages", "info": {"singularName": "chat-message", "pluralName": "chat-messages", "displayName": "Chat Message", "description": "Stores chat message data"}, "options": {"draftAndPublish": false}, "attributes": {"content": {"type": "text", "required": true}, "direction": {"type": "enumeration", "enum": ["incoming", "outgoing"], "required": true}, "timestamp": {"type": "datetime", "required": true, "default": "now"}, "session": {"type": "relation", "relation": "manyToOne", "target": "api::chat-session.chat-session", "inversedBy": "messages"}}}