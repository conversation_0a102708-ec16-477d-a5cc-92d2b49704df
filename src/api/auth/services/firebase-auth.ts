import * as admin from 'firebase-admin';
import { errors } from '@strapi/utils';
const { ApplicationError, NotFoundError } = errors;

/**
 * Firebase Authentication Service
 */
export default {
  // Initialize Firebase if it hasn't been initialized yet
  initializeFirebase() {
    if (!admin.apps.length) {
      try {
        admin.initializeApp({
          credential: admin.credential.cert({
            projectId: process.env.FIREBASE_PROJECT_ID,
            clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
            privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
          }),
        });
        strapi.log.info('Firebase Auth initialized successfully');
      } catch (error) {
        strapi.log.error('Firebase Auth initialization error:', error);
        throw new Error('Failed to initialize Firebase Auth');
      }
    }
    return admin;
  },

  // Verify Firebase ID token and return the decoded token
  async verifyIdToken(idToken) {
    try {
      this.initializeFirebase();
      const decodedToken = await admin.auth().verifyIdToken(idToken);
      return decodedToken;
    } catch (error) {
      strapi.log.error('Firebase token verification error:', error);
      throw new Error(`Invalid Firebase token: ${error.message}`);
    }
  },

  // Authenticate user with Firebase ID token
  async authenticate(idToken, ctx = null) {
    try {
      // Verify the ID token
      const decodedToken = await this.verifyIdToken(idToken);
      const { email, name, picture, uid } = decodedToken;

      if (!email) {
        throw new Error('Email not found in Firebase token');
      }

      // Extract referral URL from context if available
      const referralUrl = ctx?.state?.referralUrl || ctx?.cookies?.get('referral_url') || null;

      strapi.log.debug(`Firebase auth with referral URL: ${referralUrl}`);

      // Check if user exists in Strapi
      let user = await strapi.query('plugin::users-permissions.user').findOne({
        where: { email },
      });

      // Track if this is a new user for referral tracking
      const isNewUser = !user;

      // If no user exists, create one
      if (!user) {
        // Get default role
        const role = await strapi
          .query('plugin::users-permissions.role')
          .findOne({ where: { type: 'authenticated' } });

        if (!role) {
          throw new Error('Authenticated role not found');
        }

        // Create user with Firebase data
        user = await strapi.plugins['users-permissions'].services.user.add({
          username: email.split('@')[0],
          email,
          provider: 'firebase',
          confirmed: true, // Firebase already verified the email
          blocked: false,
          role: role.id,
          fullname: name || email.split('@')[0],
          profilePicture: picture || null,
          firebaseUid: uid,
        });

        // Process referral if referral URL exists for a new user
        if (referralUrl && isNewUser) {
          try {
            // Use the referrer-link service to track the conversion
            // Pass the newly created user to the trackLeadByUrl method
            await strapi
              .service('api::referrer-link.referrer-link')
              .trackLeadByUrl(referralUrl, user);
          } catch (refErr) {
            // Log error but don't fail authentication
            strapi.log.error('Error processing referral:', refErr);
          }
        }
      }

      // Generate JWT token
      const jwt = strapi.plugins['users-permissions'].services.jwt.issue({
        id: user.id,
      });

      // Remove sensitive data
      const { password, resetPasswordToken, confirmationToken, ...sanitizedUser } = user;

      return {
        jwt,
        user: sanitizedUser,
      };
    } catch (error) {
      strapi.log.error('Firebase authentication error:', error);
      throw new Error(`Firebase authentication failed: ${error.message}`);
    }
  },

  // Get Firebase auth URL for Google sign-in
  getGoogleAuthUrl() {
    const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
    return `${frontendUrl}/auth/firebase-google`; // Route on frontend that triggers Firebase Google Auth
  },
};
