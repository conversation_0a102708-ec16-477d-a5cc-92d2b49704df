{"kind": "collectionType", "collectionName": "traffic_webs", "info": {"singularName": "traffic-web", "pluralName": "traffic-webs", "displayName": "Traffic Web", "description": ""}, "options": {"draftAndPublish": false}, "attributes": {"affiliate": {"type": "relation", "relation": "manyToOne", "target": "api::affiliate.affiliate", "inversedBy": "traffic_webs"}, "bounce_rate": {"type": "decimal"}, "page_per_visit": {"type": "decimal"}, "visits": {"type": "integer"}, "time_on_site": {"type": "decimal"}, "month": {"type": "integer"}, "year": {"type": "integer"}, "chart": {"type": "json"}, "url": {"type": "text"}, "top_keyword": {"type": "json"}, "summary_keywords": {"type": "json"}, "top_countries": {"type": "json", "comment": "Stores country traffic distribution data from SimilarWeb"}, "traffic_sources": {"type": "json", "comment": "Stores referral traffic distribution data from SimilarWeb"}, "global_rank": {"type": "integer", "comment": "Stores the global rank position from SimilarWeb"}, "country_rank": {"type": "json", "comment": "Stores country-specific rank data including country code and rank position"}, "category_rank": {"type": "json", "comment": "Stores category-specific rank data including category name and rank position"}}}