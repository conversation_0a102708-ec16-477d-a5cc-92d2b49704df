/**
 * referral-commission service
 */

import { factories } from '@strapi/strapi';
import { logWarning } from '../../../utils/cloudwatch-logger';

export default factories.createCoreService(
  'api::referral-commission.referral-commission',
  ({ strapi }) => ({
    // Create commission based on subscription tier and user
    async createCommission(subscriptionTier: any, user: any): Promise<any> {
      try {
        console.log(`Creating commission for user ${user.id} with tier ${subscriptionTier.name}`);

        // Calculate the gross amount using the same logic as subscription tier
        const gross = this.calculateGrossAmount(subscriptionTier);

        if (gross <= 0) {
          console.log('Tier price is 0 or negative, skipping commission creation');
          return null;
        }

        // Find referrer for this user first
        const referrals: any = await strapi.entityService.findMany('api::referral.referral', {
          filters: {
            user: {
              id: user.id,
            },
          },
          populate: {
            referrer: {
              fields: ['id', 'balance', 'total_earnings', 'custom_commission_percentage'],
            },
          },
          limit: 1,
        });

        if (!referrals || referrals.length === 0) {
          console.log(`No referrer found for user ${user.id}, skipping commission creation`);
          return null;
        }

        const referrer = referrals[0].referrer;

        if (!referrer) {
          console.log(
            `No referrer found in referral ${referrals[0].id}, skipping commission creation`
          );
          return null;
        }

        // Get commission configuration
        const commissionConfig = await strapi.entityService.findMany(
          'api::commission-config.commission-config'
        );

        if (!commissionConfig) {
          console.log('Commission config not found, skipping commission creation');
          return null;
        }

        // Determine tier type and calculate commission percentage with referrer custom rate
        const commissionPercentage = this.getCommissionPercentage(
          subscriptionTier,
          commissionConfig,
          referrer
        );

        if (commissionPercentage <= 0) {
          console.log(`No commission percentage found for tier ${subscriptionTier.name}`);
          return null;
        }

        // Calculate commission amount
        const commissionAmount = (gross * commissionPercentage) / 100;

        console.log(
          `Creating commission for referrer ${referrer.id} with amount ${commissionAmount}`
        );

        // Create the commission record
        const commission = await strapi.entityService.create(
          'api::referral-commission.referral-commission',
          {
            data: {
              referrer: referrer.id,
              referral: referrals[0].id,
              subscription_tier: subscriptionTier.id,
              gross_sale_amount: gross,
              commission_percentage: commissionPercentage,
              commission_amount: commissionAmount,
              commission_status: 'pending',
            },
          }
        );

        // update balance to referrer
        await strapi.entityService.update('api::referrer.referrer', referrer.id, {
          data: {
            balance: referrer.balance + commissionAmount,
            total_earnings: referrer.total_earnings + commissionAmount,
          },
        });

        console.log('Commission created successfully:', commission);
        return commission;
      } catch (error) {
        console.error('Error creating commission:', error);
        throw error;
      }
    },

    // Calculate gross amount based on subscription tier pricing logic
    calculateGrossAmount(subscriptionTier: any): number {
      const basePrice = Number(subscriptionTier.price);

      if (basePrice <= 0) {
        return 0;
      }

      // Apply the same pricing calculation logic as subscription tier
      const interval = subscriptionTier.stripe_recurring_interval || 'month';
      let grossAmount = basePrice;

      // Calculate based on interval - this matches the subscription tier logic
      if (interval === 'year') {
        grossAmount = basePrice * 12; // Yearly pricing
      } else if (interval === 'quarter') {
        grossAmount = basePrice * 3; // Quarterly pricing
      }
      // For 'month', use base price as is

      return grossAmount;
    },

    // Determine commission percentage based on referrer custom rate, then tier type
    getCommissionPercentage(subscriptionTier: any, commissionConfig: any, referrer?: any): number {
      // First priority: Check if referrer has a custom commission percentage
      if (
        referrer &&
        referrer.custom_commission_percentage !== null &&
        referrer.custom_commission_percentage !== undefined
      ) {
        const customPercentage = Number(referrer.custom_commission_percentage);
        if (customPercentage > 0) {
          console.log(
            `Using custom commission percentage ${customPercentage}% for referrer ${referrer.id}`
          );
          return customPercentage;
        }
      }

      // Second priority: Tier-based commission calculation (existing logic)
      const tierName = subscriptionTier.name?.toLowerCase() || '';

      // Check if tier is premium
      const premiumTierNames = commissionConfig.premium_tier_names || '';
      if (premiumTierNames) {
        const premiumNames = premiumTierNames
          .split(',')
          .map((name: string) => name.trim().toLowerCase());
        const isPremium = premiumNames.some((name: string) => tierName.includes(name));
        if (isPremium) {
          return Number(commissionConfig.premium_tier_percentage) || 0;
        }
      }

      // Check if tier is pro
      const proTierNames = commissionConfig.pro_tier_names || '';
      if (proTierNames) {
        const proNames = proTierNames.split(',').map((name: string) => name.trim().toLowerCase());
        const isPro = proNames.some((name: string) => tierName.includes(name));
        if (isPro) {
          return Number(commissionConfig.pro_tier_percentage) || 0;
        }
      }

      // Third priority: Return default commission percentage
      return Number(commissionConfig.default_commission_percentage) || 0;
    },

    // Get all commissions for a referrer with date validation
    async getReferrerCommissions(referrerDocId: string, filters: any = {}) {
      // Sanitize date filters before querying
      const sanitizedFilters = this.sanitizeDateFilters(filters);

      return await strapi.entityService.findMany('api::referral-commission.referral-commission', {
        filters: {
          referrer: {
            documentId: referrerDocId,
          },
          ...sanitizedFilters,
        },
        populate: {
          referrer: true,
          referral: true,
          subscription_tier: true,
        },
        sort: { createdAt: 'desc' },
      });
    },

    // Helper method to sanitize date filters
    sanitizeDateFilters(filters: any) {
      const dateFields = ['review_due_date', 'payment_date', 'createdAt', 'updatedAt'];
      const sanitized = { ...filters };

      dateFields.forEach((field) => {
        if (sanitized[field]) {
          const validatedFilter = this.validateDateFilter(sanitized[field], field);
          if (validatedFilter === undefined) {
            delete sanitized[field];
          } else {
            sanitized[field] = validatedFilter;
          }
        }
      });

      return sanitized;
    },

    // Helper method to validate individual date filters
    validateDateFilter(dateFilter: any, fieldName: string) {
      if (typeof dateFilter === 'string') {
        // Single date string - validate and convert to ISO
        const date = new Date(dateFilter);
        if (isNaN(date.getTime())) {
          logWarning('ReferralCommissionService', `Invalid date format for ${fieldName}`, {
            invalidValue: dateFilter,
            action: 'removing filter',
          });
          return undefined;
        }
        return date.toISOString();
      } else if (typeof dateFilter === 'object' && dateFilter !== null) {
        // Date filter object with operators like $gt, $lt, etc.
        const validatedFilter: any = {};
        Object.keys(dateFilter).forEach((operator) => {
          const value = dateFilter[operator];
          if (typeof value === 'string') {
            const date = new Date(value);
            if (isNaN(date.getTime())) {
              logWarning(
                'ReferralCommissionService',
                `Invalid date format for ${fieldName}.${operator}`,
                {
                  invalidValue: value,
                  action: 'removing filter',
                }
              );
            } else {
              validatedFilter[operator] = date.toISOString();
            }
          } else {
            validatedFilter[operator] = value;
          }
        });
        return Object.keys(validatedFilter).length > 0 ? validatedFilter : undefined;
      }

      return dateFilter;
    },

    // Get commissions for a referrer with query parameters (used by controller)
    async getCommissionsWithValidation(referrerDocId: string, queryParams: any = {}) {
      // Extract and validate filters
      const filters = queryParams.filters || {};
      const sanitizedFilters = this.sanitizeDateFilters(filters);

      // Build the complete query
      const query = {
        filters: {
          referrer: {
            documentId: referrerDocId,
          },
          ...sanitizedFilters,
        },
        populate: {
          referrer: true,
          subscription_tier: {
            filters: {
              publishedAt: { $ne: null }, // Only get published subscription tiers
            },
          },
          referral: {
            populate: { user: true },
          },
        },
        sort: queryParams.sort || { createdAt: 'desc' },
        pagination: queryParams.pagination,
      };

      return await strapi.entityService.findMany(
        'api::referral-commission.referral-commission',
        query
      );
    },

    // Update commission status
    async updateCommissionStatus(commissionId: number, status: string, paidDate?: string) {
      const updateData: any = {
        status,
      };

      if (status === 'paid' && paidDate) {
        updateData.paid_date = paidDate;
      }

      return await strapi.entityService.update(
        'api::referral-commission.referral-commission',
        commissionId,
        {
          data: updateData,
        }
      );
    },

    // Get commission statistics for a referrer
    async getCommissionStats(referrerId: string | number, useDocumentId: boolean = false) {
      try {
        const filterKey = useDocumentId ? 'documentId' : 'id';
        const filterValue = useDocumentId ? referrerId : Number(referrerId);

        const referralCommissions = await strapi.entityService.findMany(
          'api::referral-commission.referral-commission',
          {
            filters: {
              referrer: {
                [filterKey]: filterValue,
              },
            },
            fields: ['commission_amount', 'gross_sale_amount', 'commission_status'],
          }
        );

        // Calculate total earnings and revenue
        const totalEarnings = referralCommissions.reduce(
          (sum, commission) => sum + (commission.commission_amount || 0),
          0
        );
        const totalRevenue = referralCommissions.reduce(
          (sum, commission) => sum + (commission.gross_sale_amount || 0),
          0
        );

        // Calculate commission stats
        const commissionStats = {
          totalCommissions: referralCommissions.length,
          pendingCommissions: referralCommissions.filter((c) => c.commission_status === 'pending')
            .length,
          readyCommissions: referralCommissions.filter((c) => c.commission_status === 'ready')
            .length,
          paidCommissions: referralCommissions.filter((c) => c.commission_status === 'paid').length,
          totalEarnings: totalEarnings,
          totalRevenue: totalRevenue,
          pendingEarnings: referralCommissions
            .filter((c) => c.commission_status === 'pending')
            .reduce((sum, c) => sum + (Number(c.commission_amount) || 0), 0),
          readyEarnings: referralCommissions
            .filter((c) => c.commission_status === 'ready')
            .reduce((sum, c) => sum + (Number(c.commission_amount) || 0), 0),
          paidEarnings: referralCommissions
            .filter((c) => c.commission_status === 'paid')
            .reduce((sum, c) => sum + (Number(c.commission_amount) || 0), 0),
        };

        return commissionStats;
      } catch (error) {
        console.error('Error getting commission stats:', error);
        throw error;
      }
    },
  })
);
