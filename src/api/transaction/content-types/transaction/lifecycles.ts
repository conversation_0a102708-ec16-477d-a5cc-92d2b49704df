async function beforeFindMany(event: any) {
  // Authentication and request limit validation now handled by middleware
  const { params } = event;

  console.log('params', params);

  // Admin requests are now handled by the content-manager-transaction middleware
  // This avoids pagination conflicts
  if (params.isFromAdmin) {
    console.log('Admin request detected - will be handled by middleware');
  }
}

async function afterFindMany(event: any) {
  const { result, params } = event;

  console.log('Transaction afterFindMany result count:', result?.length || 0);

  // Admin requests are handled by middleware, so this won't be called for those
  if (params?.isFromAdmin) {
    console.log('Admin request - handled by middleware');
  }
}

export default {
  beforeFindMany,
  afterFindMany,
};
