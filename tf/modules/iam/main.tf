resource "aws_iam_policy" "ecs_s3_access" {
  name        = "${var.environment}-ecs-s3-access"
  description = "Policy for ECS tasks to access S3 bucket"

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Action = [
          "s3:GetObject",
          "s3:PutObject",
          "s3:ListBucket"
        ],
        Effect   = "Allow",
        Resource = [
          "${var.s3_bucket_arn}",
          "${var.s3_bucket_arn}/*"
        ]
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "ecs_task_execution_policy_s3" {
  role       = var.ecs_task_execution_role
  policy_arn = aws_iam_policy.ecs_s3_access.arn
}
